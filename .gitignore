### Python ###
# Byte-compiled files
__pycache__/
*.py[cod]

# Virtual environments
venv/
env/
.venv/
.env/

# Distribution / packaging
build/
dist/
*.egg-info/
*.egg
pip-wheel-metadata/
wheelhouse/

# Testing
.coverage
.coverage.*
htmlcov/
.tox/
.pytest_cache/
.nox/

# Documentation
_build/
docs/_build/

### IDEs ###
# VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.code-workspace

# PyCharm/IntelliJ
.idea/
*.iws
*.iml
*.ipr

# MacOS
.DS_Store

# Windows
Thumbs.db
Desktop.ini

### Project Specific ###
# Configuration
config.ini
.env
.env.*
secrets.yaml

# Logs
*.log
logs/

# Data files
*.db
*.sqlite3
storage/
uploads/
datasets/

# Large model files
*.h5
*.pt
*.pth
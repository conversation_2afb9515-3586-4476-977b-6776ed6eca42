import os
import time
import logging
from toolbox import promote_file_to_downloadzone

def apply_slice_formula_repair(formula_protector, protected_sp_file_contents, original_sp_file_contents, fp, chatbot, plugin_kwargs, get_effective_md_setting):
    """
    应用切片公式修复功能
    
    Args:
        formula_protector: 公式保护器实例
        protected_sp_file_contents: 带掩码的切片内容列表
        original_sp_file_contents: 原始切片内容列表
        fp: 当前处理的文件路径
        chatbot: 聊天机器人实例
        plugin_kwargs: 插件参数
        get_effective_md_setting: 获取设置的函数
        
    Returns:
        tuple: (修复后的带掩码切片列表, 修复后的原始切片列表, 修复记录)
    """
    # 检查是否启用切片公式修复功能
    enable_slice_formula_repair = plugin_kwargs.get("enable_slice_formula_repair", False) or get_effective_md_setting(plugin_kwargs, "enable_slice_formula_repair", False)
    
    if not enable_slice_formula_repair:
        return protected_sp_file_contents, original_sp_file_contents, []
    
    logging.info(f"已启用切片公式修复功能")
    # 创建原始内容和保护后内容的副本用于修复
    repaired_protected_sp_file_contents = protected_sp_file_contents.copy()
    repaired_original_sp_file_contents = original_sp_file_contents.copy()
    
    # 执行切片公式修复
    repaired_protected_sp_file_contents, repaired_original_sp_file_contents, repair_records = formula_protector.repair_slice_formulas(
        repaired_protected_sp_file_contents, repaired_original_sp_file_contents
    )
    
    # 记录修复信息到chatbot
    total_repairs = len(repair_records)
    chatbot.append([f"切片公式修复", f"已完成切片间公式修复，共处理 {total_repairs} 个问题"])
    
    # 生成修复记录报告（无论是否有修复都生成报告）
    temp_dir = os.path.join(os.path.dirname(fp), 'temp')
    os.makedirs(temp_dir, exist_ok=True)
    now_str = time.strftime('%Y%m%d-%H%M', time.localtime())
    repair_report_path = os.path.join(temp_dir, f'切片首尾的数学公式修复报告{now_str}.md')
    
    with open(repair_report_path, 'w', encoding='utf-8') as f:
        f.write('# 切片首尾的数学公式修复报告\n\n')
        f.write(f'## 报告生成时间\n\n')
        f.write(f'{time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}\n\n')
        f.write(f'## 处理文件\n\n')
        f.write(f'{fp}\n\n')
        f.write(f'## 修复统计\n\n')
        f.write(f'共处理问题数: {total_repairs}\n\n')
        
        # 分离处理详情和其他记录
        process_details = []
        other_records = []
        for record in repair_records:
            if record.get("type") == "process_details":
                process_details.append(record)
            else:
                other_records.append(record)
        
        # 输出处理详情
        if process_details:
            f.write('## 处理过程详情\n\n')
            for process_record in process_details:
                details = process_record.get("details", [])
                for detail in details:
                    f.write(f'### 切片 {detail["slice_index"]} 处理\n\n')
                    f.write(f'操作类型: {detail["action"]}\n\n')
                    if "has_repair" in detail:
                        f.write(f'是否修复: {"是" if detail["has_repair"] else "否"}\n\n')
                    if "has_head_repair" in detail:
                        f.write(f'头部修复: {"是" if detail["has_head_repair"] else "否"}\n\n')
                    if "has_tail_repair" in detail:
                        f.write(f'尾部修复: {"是" if detail["has_tail_repair"] else "否"}\n\n')
                    if "has_trailing_fragment" in detail:
                        f.write(f'存在尾部片段: {"是" if detail["has_trailing_fragment"] else "否"}\n\n')
                    if "fragment_content" in detail:
                        f.write(f'尾部片段内容: {detail["fragment_content"]}\n\n')
                    f.write('\n')
        
        # 输出其他修复记录
        if other_records:
            f.write('## 详细修复记录\n\n')
            for i, record in enumerate(other_records):
                f.write(f'### 修复记录 {i+1}\n\n')
                f.write(f'类型: {record.get("type", "unknown")}\n\n')
                f.write(f'操作: {record.get("action", "unknown")}\n\n')
                
                if "original_masked" in record:
                    f.write('原始带掩码内容:\n')
                    f.write(f'```\n{record["original_masked"]}\n```\n\n')
                    
                if "repaired_masked" in record:
                    f.write('修复后带掩码内容:\n')
                    f.write(f'```\n{record["repaired_masked"]}\n```\n\n')
                    
                if "original_text" in record:
                    f.write('原始文本内容:\n')
                    f.write(f'```\n{record["original_text"]}\n```\n\n')
                    
                if "repaired_text" in record:
                    f.write('修复后文本内容:\n')
                    f.write(f'```\n{record["repaired_text"]}\n```\n\n')
                    
                if "masked_content" in record:
                    f.write('带掩码内容片段:\n')
                    f.write(f'```\n{record["masked_content"]}\n```\n\n')
                    
                if "original_content" in record:
                    f.write('原始内容片段:\n')
                    f.write(f'```\n{record["original_content"]}\n```\n\n')
                    
                f.write('---\n\n')
        else:
            f.write('## 未发现需要修复的问题\n\n')
            f.write('经过检查，未发现需要修复的切片间公式问题。\n\n')
    
    # 将修复报告添加到下载区域
    promote_file_to_downloadzone(repair_report_path, chatbot=chatbot)
    chatbot.append([f"切片公式修复报告", f"已生成切片公式修复报告: {os.path.basename(repair_report_path)}"])
    
    return repaired_protected_sp_file_contents, repaired_original_sp_file_contents, repair_records
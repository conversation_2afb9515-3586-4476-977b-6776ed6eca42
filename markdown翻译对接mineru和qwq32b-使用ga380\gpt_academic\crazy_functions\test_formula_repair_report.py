import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from protect_math import MathFormulaProtector
import tempfile
import time

def test_formula_repair_report():
    """
    测试修改后的切片公式修复报告功能
    """
    # 创建临时目录用于测试
    temp_dir = tempfile.mkdtemp()
    test_file_path = os.path.join(temp_dir, "test.md")
    
    protector = MathFormulaProtector()
    
    # 创建测试数据：模拟两个切片，其中一个公式被分割到两个切片中
    # 带掩码的切片
    masked_slices = [
        "这是第一段内容 __FORMULA_INLINE_DOLLAR_0__ 这是第一段的结尾\n$$\nH = \\frac{1}{2}mv^2 + V(x)",
        "$$\n这是第二段内容 __FORMULA_INLINE_DOLLAR_0__ 这是第二段的结尾"
    ]
    
    # 对应的原始切片
    original_slices = [
        "这是第一段内容 $E = mc^2$ 这是第一段的结尾\n$$\nH = \\frac{1}{2}mv^2 + V(x)",
        "$$\n这是第二段内容 $F = ma$ 这是第二段的结尾"
    ]
    
    print("修复前:")
    print("带掩码切片1:", masked_slices[0])
    print("带掩码切片2:", masked_slices[1])
    print("原始切片1:", original_slices[0])
    print("原始切片2:", original_slices[1])
    
    # 应用切片修复
    repaired_masked_slices, repaired_original_slices, repair_records = protector.repair_slice_formulas(
        masked_slices, original_slices
    )
    
    print("\n修复后:")
    print("修复后带掩码切片1:", repaired_masked_slices[0])
    print("修复后带掩码切片2:", repaired_masked_slices[1])
    print("修复后原始切片1:", repaired_original_slices[0])
    print("修复后原始切片2:", repaired_original_slices[1])
    
    print("\n修复记录:")
    for i, record in enumerate(repair_records):
        print(f"记录 {i+1}: {record}")
    
    # 验证原始切片是否保持原样
    assert repaired_original_slices[0] == original_slices[0], "原始切片1被错误修改"
    assert repaired_original_slices[1] == original_slices[1], "原始切片2被错误修改"
    print("\n测试通过：原始切片保持原样未被修改")
    
    # 验证带掩码切片是否被处理
    print("\n测试完成")
    
    # 测试报告生成
    print("\n测试报告生成...")
    from slice_formula_repair import apply_slice_formula_repair
    
    # 模拟chatbot
    class MockChatbot:
        def __init__(self):
            self.messages = []
            
        def append(self, message):
            self.messages.append(message)
            
    chatbot = MockChatbot()
    
    # 模拟plugin_kwargs
    plugin_kwargs = {"enable_slice_formula_repair": True}
    
    def get_effective_md_setting(kwargs, key, default):
        return kwargs.get(key, default)
    
    # 应用切片公式修复并生成报告
    result = apply_slice_formula_repair(
        protector, masked_slices, original_slices, test_file_path, chatbot, plugin_kwargs, get_effective_md_setting
    )
    
    print("Chatbot消息:")
    for msg in chatbot.messages:
        print(msg)
    
    # 检查报告是否生成
    report_dir = os.path.join(os.path.dirname(test_file_path), 'temp')
    if os.path.exists(report_dir):
        reports = [f for f in os.listdir(report_dir) if f.startswith('切片首尾的数学公式修复报告')]
        if reports:
            print(f"报告已生成: {reports[0]}")
            # 读取并显示报告内容
            report_path = os.path.join(report_dir, reports[0])
            with open(report_path, 'r', encoding='utf-8') as f:
                print("\n报告内容预览:")
                content = f.read()
                print(content[:500] + "..." if len(content) > 500 else content)
        else:
            print("未找到报告文件")
    else:
        print("报告目录未创建")

if __name__ == "__main__":
    test_formula_repair_report()
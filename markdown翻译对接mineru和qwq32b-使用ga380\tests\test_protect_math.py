import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../gpt_academic/crazy_functions')))
from protect_math import MathFormulaProtector

# 测试用例列表（输入，期望还原后）
test_cases = [
    # 1. 单个行内公式
    ("这是$E=mc^2$。", "这是$E=mc^2$。"),
    # 2. 单个独立公式
    ("$$a^2+b^2=c^2$$", "$$a^2+b^2=c^2$$"),
    # 3. 单个LaTeX环境公式
    ("\\begin{equation}a^2+b^2=c^2\\end{equation}", "\\begin{equation}a^2+b^2=c^2\\end{equation}"),
    # 4. 单个\\[...\\]公式
    ("\\[a^2+b^2=c^2\\]", "\\[a^2+b^2=c^2\\]"),
    # 5. 单个\\(...\\)公式
    ("这是\\(E=mc^2\\)公式。", "这是\\(E=mc^2\\)公式。"),
    # 6. 多个行内公式
    ("$a$+$b$=$c$", "$a$+$b$=$c$"),
    # 7. 多个独立公式
    ("$$x$$\n$$y$$", "$$x$$\n$$y$$"),
    # 8. 多个LaTeX环境
    ("\\begin{equation}x\\end{equation}\\begin{align}y\\end{align}", "\\begin{equation}x\\end{equation}\\begin{align}y\\end{align}"),
    # 9. 多个\\[...\\]
    ("\\[x\\]\\[y\\]", "\\[x\\]\\[y\\]"),
    # 10. 多个\\(...\\)
    ("\\(x\\)\\(y\\)", "\\(x\\)\\(y\\)"),
    # 11. 行内与独立混合
    ("$a$\n$$b$$", "$a$\n$$b$$"),
    # 12. 环境与行内混合
    ("\\begin{equation}a\\end{equation}$b$", "\\begin{equation}a\\end{equation}$b$"),
    # 13. 环境与独立混合
    ("\\begin{equation}a\\end{equation}$$b$$", "\\begin{equation}a\\end{equation}$$b$$"),
    # 14. 所有类型混合
    ("$a$\\[b\\]$$c$$\\begin{equation}d\\end{equation}\\(e\\)", "$a$\\[b\\]$$c$$\\begin{equation}d\\end{equation}\\(e\\)"),
    # 15. 嵌套（虽然不推荐，但要测试）
    ("$a$$b$", "$a$$b$"),
    # 16. 空文本
    ("", ""),
    # 17. 无公式文本
    ("这是普通文本。", "这是普通文本。"),
    # 18. 公式中有特殊字符
    ("$a_1^2+b_2^2=c_3^2$", "$a_1^2+b_2^2=c_3^2$"),
    # 19. 公式中有中文
    ("$能量=质量\times光速^2$", "$能量=质量\times光速^2$"),
    # 20. 公式与代码块混合
    ("```python\nprint(1)\n```$a$", "```python\nprint(1)\n```$a$"),
    # 21. 公式与图片混合
    ("![img](url)$a$", "![img](url)$a$"),
    # 22. 公式与表格混合
    ("|a|b|$c$|", "|a|b|$c$|"),
    # 23. 公式与标题混合
    ("## 标题 $a$", "## 标题 $a$"),
    # 24. 公式与列表混合
    ("- $a$", "- $a$"),
    # 25. 公式与引用混合
    ("> $a$", "> $a$"),
    # 26. 公式与链接混合
    ("[link](url)$a$", "[link](url)$a$"),
    # 27. 公式与转义字符混合
    ("\\$a$", "\\$a$"),
    # 28. 公式与多行文本
    ("第一行\n$a$\n第二行", "第一行\n$a$\n第二行"),
    # 29. 公式与多种分隔符
    ("$a$ $$b$$ \\[c\\] \\(d\\)", "$a$ $$b$$ \\[c\\] \\(d\\)"),
    # 30. 复杂嵌套与组合
    ("$a$ $$b$$ $c$ \\[d\\] \\(e\\) \\begin{align}f\\end{align}", "$a$ $$b$$ $c$ \\[d\\] \\(e\\) \\begin{align}f\\end{align}"),
    # 31. 公式与注释混合
    ("<!-- 注释 -->$a$", "<!-- 注释 -->$a$"),
    # 32. 公式与空格混合
    (" $a$ ", " $a$ "),
    # 33. 公式与换行符混合
    ("$a$\n", "$a$\n"),
    # 34. 公式与多种语言字符
    ("$aαβγ$", "$aαβγ$"),
    # 35. 公式与emoji混合
    ("$a$😀", "$a$😀"),
    
    # ===== 新增测试用例 =====
    # 36. 多行复杂公式
    ("""$$
    f(x) = \\int_{-\\infty}^{\\infty} \\hat{f}(\\xi) e^{2\\pi i \\xi x} d\\xi
    $$""", 
    """$$
    f(x) = \\int_{-\\infty}^{\\infty} \\hat{f}(\\xi) e^{2\\pi i \\xi x} d\\xi
    $$"""),
    
    # 37. 连续美元符号
    ("这里是$$$a$$，应该正确处理", "这里是$$$a$$，应该正确处理"),
    
    # 38. 不匹配的标签（应保持原样）
    ("这里是$a，应该被忽略", "这里是$a，应该被忽略"),
    
    # 39. 公式内转义字符
    ("$a\\$b$", "$a\\$b$"),
    
    # 40. 嵌套环境
    ("""\\begin{align}
    f(x) = \\begin{cases}
    1 & x > 0 \\\\
    0 & x \\leq 0
    \\end{cases}
    \\end{align}""", 
    """\\begin{align}
    f(x) = \\begin{cases}
    1 & x > 0 \\\\
    0 & x \\leq 0
    \\end{cases}
    \\end{align}"""),
    
    # 41. 真实场景长文本
    ("""# 数学公式示例
    
    这里是一个行内公式 $E=mc^2$，以及一个独立公式：
    
    $$
    \\frac{\\partial u}{\\partial t} = h^2 \\left( \\frac{\\partial^2 u}{\\partial x^2} + \\frac{\\partial^2 u}{\\partial y^2} + \\frac{\\partial^2 u}{\\partial z^2} \\right)
    $$
    
    还有一个矩阵：
    
    \\begin{bmatrix}
    a & b \\\\
    c & d
    \\end{bmatrix}""", 
    """# 数学公式示例
    
    这里是一个行内公式 $E=mc^2$，以及一个独立公式：
    
    $$
    \\frac{\\partial u}{\\partial t} = h^2 \\left( \\frac{\\partial^2 u}{\\partial x^2} + \\frac{\\partial^2 u}{\\partial y^2} + \\frac{\\partial^2 u}{\\partial z^2} \\right)
    $$
    
    还有一个矩阵：
    
    \\begin{bmatrix}
    a & b \\\\
    c & d
    \\end{bmatrix}"""),
    
    # 42. 行内公式紧邻文本
    ("文本$a$紧邻", "文本$a$紧邻"),
    
    # 43. 结尾处的公式
    ("文本以公式结尾$a$", "文本以公式结尾$a$"),
    
    # 44. 开头处的公式
    ("$a$文本以公式开头", "$a$文本以公式开头"),
    
    # 45. 多个连续公式
    ("$a$$b$$c$", "$a$$b$$c$"),
    
    # 46. 翻译模拟测试（模拟翻译过程中的占位符处理）
    ("这是一个公式 $E=mc^2$ 的测试。", "这是一个公式 $E=mc^2$ 的测试。"),

    # 47. 长段落混合公式与文本
    ("""在本节中，我们将讨论能量守恒定律。能量的表达式为 $E=mc^2$，这是著名的爱因斯坦公式。

    进一步地，考虑如下积分：
    $$
    \\int_0^1 x^2 dx = \\frac{1}{3}
    $$
    该积分结果如上。

    代码示例：
    ```python
    def energy(m, c):
        return m * c ** 2
    ```
    其中 $m$ 表示质量，$c$ 表示光速。
    """, 
    """在本节中，我们将讨论能量守恒定律。能量的表达式为 $E=mc^2$，这是著名的爱因斯坦公式。

    进一步地，考虑如下积分：
    $$
    \\int_0^1 x^2 dx = \\frac{1}{3}
    $$
    该积分结果如上。

    代码示例：
    ```python
    def energy(m, c):
        return m * c ** 2
    ```
    其中 $m$ 表示质量，$c$ 表示光速。
    """),

    # 48. 长表格与公式混合
    ("""| 变量 | 公式 | 说明 |
    | ---- | ---- | ---- |
    | $a$ | $a^2$ | 平方 |
    | $b$ | $b^2$ | 平方 |
    | $c$ | $c^2$ | 平方 |
    | $d$ | $d^2$ | 平方 |
    | $e$ | $e^2$ | 平方 |
    """, 
    """| 变量 | 公式 | 说明 |
    | ---- | ---- | ---- |
    | $a$ | $a^2$ | 平方 |
    | $b$ | $b^2$ | 平方 |
    | $c$ | $c^2$ | 平方 |
    | $d$ | $d^2$ | 平方 |
    | $e$ | $e^2$ | 平方 |
    """),

    # 49. 长列表与公式混合
    ("""- 质量 $m$
    - 光速 $c$
    - 能量 $E=mc^2$
    - 动量 $p=mv$
    - 力 $F=ma$
    - 加速度 $a=\\frac{dv}{dt}$
    - 速度 $v=\\frac{dx}{dt}$
    """, 
    """- 质量 $m$
    - 光速 $c$
    - 能量 $E=mc^2$
    - 动量 $p=mv$
    - 力 $F=ma$
    - 加速度 $a=\\frac{dv}{dt}$
    - 速度 $v=\\frac{dx}{dt}$
    """),

    # 50. 长上下文多种结构混合
    ("""# 物理公式总览

    ## 经典力学
    牛顿第二定律：$F=ma$
    功的定义：$W=Fd$
    功率：$P=\\frac{W}{t}$

    ## 电磁学
    电场强度：$E=\\frac{F}{q}$
    磁感应强度：$B=\\frac{F}{qv\\sin\\theta}$

    ## 热学
    热量：$Q=cm\\Delta T$
    
    ## 量子力学
    薛定谔方程：
    $$
    i\\hbar\\frac{\\partial}{\\partial t}\\Psi=\\hat{H}\\Psi
    $$
    """, 
    """# 物理公式总览

    ## 经典力学
    牛顿第二定律：$F=ma$
    功的定义：$W=Fd$
    功率：$P=\\frac{W}{t}$

    ## 电磁学
    电场强度：$E=\\frac{F}{q}$
    磁感应强度：$B=\\frac{F}{qv\\sin\\theta}$

    ## 热学
    热量：$Q=cm\\Delta T$
    
    ## 量子力学
    薛定谔方程：
    $$
    i\\hbar\\frac{\\partial}{\\partial t}\\Psi=\\hat{H}\\Psi
    $$
    """),
]

def run_tests():
    protector = MathFormulaProtector()
    passed = 0
    failed = 0
    
    print("\n===== 运行公式保护测试 =====\n")
    
    for idx, (input_text, expected_restored) in enumerate(test_cases):
        test_num = idx + 1
        protected, mapping = protector.protect(input_text)
        restored = protector.restore(protected, mapping)
        
        # 只检查还原后的结果是否正确，不检查占位符编号
        if restored == expected_restored:
            print(f"[PASS] 测试 {test_num}")
            passed += 1
        else:
            print(f"[FAIL] 测试 {test_num}")
            print(f"  输入:    {input_text[:50]}{'...' if len(input_text) > 50 else ''}")
            print(f"  保护后:  {protected[:50]}{'...' if len(protected) > 50 else ''}")
            print(f"  还原后:  {restored[:50]}{'...' if len(restored) > 50 else ''}")
            print(f"  期望:    {expected_restored[:50]}{'...' if len(expected_restored) > 50 else ''}")
            failed += 1
            
    # 添加翻译模拟测试
    print("\n===== 运行翻译模拟测试 =====\n")
    
    # 模拟翻译过程测试
    sample_text = "这是一个包含公式 $E=mc^2$ 和 $$F=ma$$ 的段落。"
    protected, mapping = protector.protect(sample_text)
    
    # 模拟翻译（简单替换中文）
    translated_protected = protected.replace("这是一个包含公式", "This is a paragraph with formula")
    translated_protected = translated_protected.replace("和", "and")
    translated_protected = translated_protected.replace("的段落。", "")  # 修改这里，包含句号一起替换
    
    # 还原公式
    final_text = protector.restore(translated_protected, mapping)
    expected_final = "This is a paragraph with formula $E=mc^2$ and $$F=ma$$"
    
    if final_text == expected_final:
        print(f"[PASS] 翻译模拟测试")
        passed += 1
    else:
        print(f"[FAIL] 翻译模拟测试")
        print(f"  原文:    {sample_text}")
        print(f"  保护后:  {protected}")
        print(f"  翻译后:  {translated_protected}")
        print(f"  还原后:  {final_text}")
        print(f"  期望:    {expected_final}")
        failed += 1
    
    print(f"\n测试结果: {passed}/{passed+failed} 通过")
    
    return passed == len(test_cases) + 1  # +1 是因为额外的翻译模拟测试

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)  # 返回退出码，便于CI/CD集成 
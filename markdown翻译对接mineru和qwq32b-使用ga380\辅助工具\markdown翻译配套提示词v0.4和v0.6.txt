# qwq32b 测试版    markdown翻译翻译markdown的prompt v0.6
增加：修补文章错误功能
增加：易读性翻译
修复：代码标签成对。只输出译文。

v0.6
一、翻译要求：
翻译MD源代码（包含latex源码）的内容翻译为目标语言。默认目标语言为中文。
中文语法，句式要求生动活泼，通俗易懂，句子流畅度要最高。句式温暖亲切，清新自然。
一切没有文采的，不生动的语句逐行润色成生动、活泼的句子。
一切死板、冰冷、复杂晦涩的句式逐行转化为通俗易懂的语句。
一切中文里没有的句式和表达转化为中文本土句式并保证通俗易懂。
仔细思考每一句话在中文里是如何恰当表达的，不要机械的翻译，输出的译文要完全像一个中文本土作者的作品。
只逐句翻译，不要有任何一个字的多余回答，比如解释和说明语句以及英文原文。
段落中的核心要点、重点概念的词语给与加粗显示。不要加粗整个段落，整个句子。


二、排版，格式要求：
appendix也要翻译。
去掉英文原文：输出翻译时不要带任何依据的英文原文。
输出的纯净性：保证输出的文本只包含纯净的译文，不输出其他任何译文之外的文本。
翻译完整性：保证每一句英文原文都要被翻译。
布局：译文要保持原文的框架结构和布局，保持原文语句先后和段落的先后顺序。
保留标题：译文保持和原文章标题的格式一一对应。
重点加黑：重点内容必须是短句或者词语才能加黑加粗，不能是长句子或者段落。
翻译输出：不要再输出中出现提示词。
翻译输出：只输出正式译文，作为书籍翻译正式输出。
标题：与原文的标题标签一一对应，不要输出新的标题。

三、风险代码规避
保留数学公式：保留所有的数学公式的内容，不要删除。
latex规格化：原文中latex数学公式标签统一转换为`$` 和 `$$`这标准的标签样式，（只针对标签修改，和匹配，务必不要更改、删除公式内容）.
latex公式纠正：latex公式有渲染错误和语法错误也必须要纠正为正规的，通用的，能正常渲染的公式。
非代码的文本务必不要写到代码块里，保持普通正文的格式。
非标题的文本务必不要写到标题标签中，保持普通正文的格式。
图片丢失：审查对比原文章中的图片链接，保证译文中图片链接一个也不要丢失。
代码块错误：尽量少用行内代码块  比如 'define'中尽量去掉单引号。段落的代码块要封闭，完整。

四、内容修正
代码修复：有些是ocr后的代码，很乱，有错字，必须主动去修复，并制作成规范的代码格式。
文字修复：正文中出现的错字也根据上下文进行修复。
修复的准确性：专业名词不要去合并翻译，要一一对应的翻译和修复。



接下来请翻译：


#   qwq32b 测试版    markdown翻译翻译markdown的prompt   v0.5
一、翻译要求：
翻译MD源代码（包含latex源码）的内容翻译为目标语言。默认目标语言为中文。
中文语法，句式要求生动活泼，通俗易懂，句子流畅度要最高。句式温暖亲切，清新自然。
一切没有文采的，不生动的语句逐行润色成生动、活泼的句子。
一切死板、冰冷、复杂晦涩的句式逐行转化为通俗易懂的语句。
一切中文里没有的句式和表达转化为中文本土句式并保证通俗易懂。
仔细思考每一句话在中文里是如何恰当表达的，不要机械的翻译，输出的译文要完全像一个中文本土作者的作品。
只逐句翻译，不要有任何一个字的多余回答，比如解释和说明语句以及英文原文。
段落中的核心要点、重点概念的词语给与加粗显示。不要加粗整个段落，整个句子。

二、排版，格式要求：
appendix也要翻译。
去掉英文原文：输出翻译时不要带任何依据的英文原文。
输出的纯净性：保证输出的文本只包含纯净的译文，不输出其他任何译文之外的文本。
翻译完整性：保证每一句英文原文都要被翻译。
布局：译文要保持原文的框架结构和布局，保持原文语句先后和段落的先后顺序。
保留标题：译文保持和原文章标题的格式一一对应。
重点加黑：重点内容必须是短句或者词语才能加黑加粗，不能是长句子或者段落。

三、风险代码规避
保留数学公式：保留所有的数学公式的内容，不要删除。
latex规格化：原文中latex数学公式标签统一转换为`$` 和 `$$`这标准的标签样式，（只针对标签修改，和匹配，务必不要更改、删除公式内容）.
latex公式纠正：latex公式有渲染错误和语法错误也必须要纠正为正规的，通用的，能正常渲染的公式。
非代码的文本务必不要写到代码块里，保持普通正文的格式。
非标题的文本务必不要写到标题标签中，保持普通正文的格式。
图片丢失：审查对比原文章中的图片链接，保证译文中图片链接一个也不要丢失。

接下来请翻译：



#  qwq32b 测试版    markdown翻译翻译markdown的prompt   v0.4版
一、翻译要求：
翻译MD源代码（包含latex源码）的内容翻译为目标语言。默认目标语言为中文。
中文语法，句式要求生动活泼，通俗易懂，句子流畅度要最高。句式温暖亲切，清新自然。
一切没有文采的，不生动的语句逐行润色成生动、活泼的句子。
一切死板、冰冷、复杂晦涩的句式逐行转化为通俗易懂的语句。
一切中文里没有的句式和表达转化为中文本土句式并保证通俗易懂。
只逐句翻译，不要有任何一个字的多余回答，比如解释和说明语句。
段落中的核心要点、重点概念的词语给与加粗显示。不要加粗整个段落，整个句子。

二、排版，格式要求：
appendix也要翻译。
去掉原文：输出翻译时不要带任何的原文，也不要漏翻译。
布局：译文要保持原文的框架结构和布局，保持原文语句和段落的先后顺序。
保留标题：译文保持和原文章标题的格式一一对应。
重点加黑：重点内容必须是短句或者词语才能加黑加粗，不能是长句子或者段落。

三、风险代码规避
保留数学公式：保留所有的数学公式的内容，不要删除。
latex规格化：原文中latex数学公式标签统一转换为`$` 和 `$$`这标准的标签样式，（只针对标签修改，和匹配，务必不要更改、删除公式内容）.
latex公式纠正：latex公式有渲染错误和语法错误也必须要纠正为正规的，通用的，能正常渲染的公式。
非代码的文本务必不要写到代码块里，保持普通正文的格式。
非标题的文本务必不要写到标题标签中，保持普通正文的格式。
图片丢失：审查对比原文章中的图片链接，保证译文中图片链接一个也不要丢失。

接下来请翻译：